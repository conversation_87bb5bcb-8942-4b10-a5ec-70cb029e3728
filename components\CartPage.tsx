import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../contexts/AuthContext';
import { TrashIcon, PlusIcon, MinusIcon, ShoppingBagIcon, MapPinIcon } from './common/Icon';
import { useAddress } from '../contexts/AddressContext';
import AddressList from './addresses/AddressList';

interface GroupedCartItem {
  id: string;
  product: any;
  variant_id?: string;
  items: string[];
  totalQuantity?: number;
  index?: number;
}

const CartPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { cartItems, cartSummary, loading, removeFromCart, updateQuantity } = useCart();
  const { addresses } = useAddress();
  const [visibleItems, setVisibleItems] = useState<Set<number>>(new Set());
  const [processingItems, setProcessingItems] = useState<Set<string>>(new Set());
  const [selectedAddressId, setSelectedAddressId] = useState<string | null>(null);

  // Helper function to get variant image path
  const getVariantImage = (productName: string, variantId: string) => {
    // Handle hand wash variants specifically
    if (productName.includes('Hand Wash')) {
      switch (variantId) {
        case 'green-apple': return '/assets/products/handwash-1.jpg';
        case 'strawberry': return '/assets/products/handwash-2.jpg';
        case 'lavender': return '/assets/products/handwash-3.jpg';
        default: return '/assets/products/handwash-three-flavors.jpg';
      }
    }
    // Add mappings for other products as needed
    return '';
  };

  // Redirect if not logged in
  useEffect(() => {
    if (!user && !loading) {
      navigate('/login');
    }
  }, [user, loading, navigate]);

  // Animate items on load
  useEffect(() => {
    const timer = setTimeout(() => {
      cartItems.forEach((_, index) => {
        setTimeout(() => {
          setVisibleItems(prev => new Set([...prev, index]));
        }, index * 100);
      });
    }, 200);

    return () => clearTimeout(timer);
  }, [cartItems]);

  const handleQuantityChange = async (cartItemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      setProcessingItems(prev => new Set([...prev, cartItemId]));
      await removeFromCart(cartItemId);
      setProcessingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(cartItemId);
        return newSet;
      });
      return;
    }

    setProcessingItems(prev => new Set([...prev, cartItemId]));
    await updateQuantity(cartItemId, newQuantity);
    setProcessingItems(prev => {
      const newSet = new Set(prev);
      newSet.delete(cartItemId);
      return newSet;
    });
  };

  const handleRemoveItem = async (cartItemId: string) => {
    setProcessingItems(prev => new Set([...prev, cartItemId]));
    await removeFromCart(cartItemId);
    setProcessingItems(prev => {
      const newSet = new Set(prev);
      newSet.delete(cartItemId);
      return newSet;
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-page-bg-light to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-accent-teal mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your cart...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen font-sans relative overflow-hidden">
      {/* Background remains the same... */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-page-bg-light to-gray-100">
        {/* Previous background patterns remain unchanged */}
      </div>

      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header remains the same... */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Shopping Cart
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Review your selected items and proceed to checkout
          </p>
        </div>

        {cartItems.length === 0 ? (
          // Empty Cart State
          <div className="text-center py-16">
            <div className="bg-white/90 backdrop-blur-sm rounded-3xl shadow-xl border border-white/50 p-12 max-w-md mx-auto">
              <div className="w-24 h-24 bg-gradient-to-br from-brand-accent-teal/20 to-brand-main-red/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <ShoppingBagIcon size={40} className="text-gray-400" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Your cart is empty</h3>
              <p className="text-gray-600 mb-8">
                Discover our amazing products and add them to your cart
              </p>
              <button
                onClick={() => navigate('/store')}
                className="bg-gradient-to-r from-brand-accent-teal to-brand-accent-teal-darker text-white px-8 py-3 rounded-full font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300"
              >
                Continue Shopping
              </button>
            </div>
          </div>
        ) : (
          // Updated Layout to include Address Selection
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
            {/* Cart Items List - Made slightly smaller */}
            <div className="lg:col-span-7 space-y-6">
              {cartItems.map((item, index) => {
                const product = item.product;
                if (!product) return null;

                const isVisible = visibleItems.has(index);
                const isProcessing = processingItems.has(item.id);

                return (
                  <div
                    key={item.id}
                    className={`bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-white/50 p-6 transition-all duration-700 ${
                      isVisible ? 'opacity-100 transform-none' : 'opacity-0 translate-y-8'
                    } ${isProcessing ? 'opacity-50' : ''}`}
                  >
                    <div className="flex flex-col sm:flex-row gap-6">
                      {/* Product Image */}
                      <div className="flex-shrink-0">
                        <div className="w-32 h-32 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl overflow-hidden">
                          {product.name.includes('Hand Wash') && item.variant_id ? (
                            <img
                              src={getVariantImage(product.name, item.variant_id)}
                              alt={product.name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <img
                              src={product.image_url}
                              alt={product.name}
                              className="w-full h-full object-cover"
                            />
                          )}
                        </div>
                      </div>

                      {/* Product Details */}
                      <div className="flex-grow">
                        <div className="flex justify-between items-start mb-4">
                          <div>
                            <h3 className="text-xl font-bold text-gray-900 mb-2">
                              {product.name}
                              {item.variant_id && product.variants && (
                                <span className="text-brand-accent-teal block text-base font-normal">
                                  {product.variants.find((v: { id: string; name: string }) => v.id === item.variant_id)?.name}
                                </span>
                              )}
                            </h3>
                            <p className="text-gray-600 text-sm mb-2">{product.category}</p>
                            {product.original_price && product.discount ? (
                              <div className="flex flex-col">
                                <div className="flex items-center space-x-2 mb-1">
                                  <span className="bg-brand-main-red text-white text-xs px-2 py-1 rounded-full font-bold">
                                    {product.discount}
                                  </span>
                                  <span className="text-sm text-gray-500 line-through">
                                    AED {product.original_price.toFixed(2)}
                                  </span>
                                </div>
                                <p className="text-brand-accent-teal font-semibold text-lg">
                                  AED {product.price.toFixed(2)}
                                </p>
                                <span className="text-xs text-brand-accent-teal font-medium">
                                  Save AED {(product.original_price - product.price).toFixed(2)} each
                                </span>
                              </div>
                            ) : (
                              <p className="text-brand-accent-teal font-semibold text-lg">
                                AED {product.price.toFixed(2)}
                              </p>
                            )}
                          </div>
                          <button
                            onClick={() => handleRemoveItem(item.id)}
                            disabled={isProcessing}
                            className="p-2 text-red-500 hover:bg-red-50 rounded-full transition-colors duration-200 disabled:opacity-50"
                            aria-label="Remove item"
                          >
                            <TrashIcon size={20} />
                          </button>
                        </div>

                        {/* Quantity Controls */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <span className="text-gray-600 font-medium">Quantity:</span>
                            <div className="flex items-center space-x-2">
                              <button
                                onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                                disabled={isProcessing || item.quantity <= 1}
                                className="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                <MinusIcon size={16} />
                              </button>
                              <span className="w-12 text-center font-semibold text-lg">
                                {item.quantity}
                              </span>
                              <button
                                onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                                disabled={isProcessing}
                                className="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors duration-200 disabled:opacity-50"
                              >
                                <PlusIcon size={16} />
                              </button>
                            </div>
                          </div>
                          <div className="text-right">
                            {product.original_price && (
                              <p className="text-sm text-gray-500 line-through">
                                AED {(product.original_price * item.quantity).toFixed(2)}
                              </p>
                            )}
                            <p className="text-lg font-bold text-gray-900">
                              AED {(product.price * item.quantity).toFixed(2)}
                            </p>
                            {product.original_price && (
                              <p className="text-sm text-brand-accent-teal font-semibold">
                                Save AED {((product.original_price - product.price) * item.quantity).toFixed(2)}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Address Selection - New section */}
            <div className="lg:col-span-5">
              {/* Order Summary */}
              <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 p-6 sticky top-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Order Summary</h3>
                
                <div className="space-y-4 mb-6">
                  {/* Show original total if there are savings */}
                  {cartSummary.totalSavings && cartSummary.totalSavings > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-500">Original Total</span>
                      <span className="text-gray-500 line-through">AED {cartSummary.originalTotal?.toFixed(2)}</span>
                    </div>
                  )}

                  {/* Show grouped products with total quantities */}
                  <div className="space-y-2">
                    {Object.values(cartItems.reduce((acc: any, item) => {
                      const product = item.product;
                      if (!product) return acc;
                      
                      const key = `${product.id}-${item.variant_id || 'default'}`;
                      if (!acc[key]) {
                        acc[key] = {
                          product,
                          variant_id: item.variant_id,
                          totalQuantity: 0,
                          totalPrice: 0
                        };
                      }
                      acc[key].totalQuantity += item.quantity;
                      acc[key].totalPrice += product.price * item.quantity;
                      return acc;
                    }, {})).map((groupedItem: any) => (
                      <div key={`${groupedItem.product.id}-${groupedItem.variant_id || 'default'}`} 
                           className="flex justify-between items-start text-sm gap-2">
                        <span className="text-gray-600 flex-1 leading-relaxed">
                          <span className="block sm:inline">{groupedItem.product.name}</span>
                          {groupedItem.variant_id && groupedItem.product.variants && (
                            <span className="block text-brand-accent-teal text-sm">
                              {groupedItem.product.variants.find((v: { id: string; name: string }) => v.id === groupedItem.variant_id)?.name}
                            </span>
                          )}
                          <span className="block sm:inline sm:ml-1">x {groupedItem.totalQuantity}</span>
                        </span>
                        <span className="font-semibold text-right flex-shrink-0">
                          AED {groupedItem.totalPrice.toFixed(2)}
                        </span>
                      </div>
                    ))}
                  </div>

                  {/* Show total savings */}
                  {cartSummary.totalSavings && cartSummary.totalSavings > 0 && (
                    <div className="flex justify-between border-t pt-2">
                      <span className="text-brand-accent-teal font-semibold">Total Savings</span>
                      <span className="text-brand-accent-teal font-bold">-AED {cartSummary.totalSavings.toFixed(2)}</span>
                    </div>
                  )}

                  <div className="border-t pt-4">
                    <div className="flex justify-between text-xl font-bold">
                      <span>Total</span>
                      <span className="text-brand-main-red">AED {cartSummary.total.toFixed(2)}</span>
                    </div>

                    {/* Highlight total savings */}
                    {cartSummary.totalSavings && cartSummary.totalSavings > 0 && (
                      <div className="mt-2 text-center">
                        <span className="bg-brand-accent-teal text-white px-4 py-2 rounded-full text-sm font-bold">
                          🎉 You saved AED {cartSummary.totalSavings.toFixed(2)} total!
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Updated checkout button section */}
                <button
                  className={`w-full bg-gradient-to-r from-brand-main-red to-brand-main-red-darker text-white py-4 rounded-full font-bold text-lg transition-all duration-300 mb-4 ${
                    selectedAddressId ? 'hover:shadow-lg transform hover:scale-105' : 'opacity-50 cursor-not-allowed'
                  }`}
                  disabled={!selectedAddressId}
                  onClick={() => {
                    if (!selectedAddressId) {
                      return;
                    }
                    // Create bill-like message
                    let message = "I'd like to place an order:\n\n";
                    message += "==============================\n";
                    message += "          ORDER SUMMARY        \n";
                    message += "==============================\n\n";

                    // Add delivery address
                    const deliveryAddress = addresses.find(addr => addr.id === selectedAddressId);
                    if (deliveryAddress) {
                      message += "DELIVERY ADDRESS:\n";
                      message += `${deliveryAddress.full_name}\n`;
                      message += `${deliveryAddress.phone_number}\n`;
                      message += `${deliveryAddress.street_address}\n`;
                      if (deliveryAddress.apartment) {
                        message += `${deliveryAddress.apartment}\n`;
                      }
                      message += `${deliveryAddress.city}, ${deliveryAddress.state} ${deliveryAddress.postal_code}\n`;
                      message += `${deliveryAddress.country}\n\n`;
                      message += "------------------------------\n\n";
                    }

                    message += "ITEMS:\n";
                    
                    cartItems.forEach(item => {
                      const product = item.product;
                      if (!product) return;
                      
                      let productText = `• ${product.name}`;
                      
                      if (item.variant_id && product.variants) {
                        const variant = product.variants.find(v => v.id === item.variant_id);
                        if (variant) {
                          productText += ` (${variant.name})`;
                        }
                      }
                      
                      productText += ` - ${item.quantity} x AED ${product.price.toFixed(2)}`;
                      const lineTotal = product.price * item.quantity;
                      productText += ` = AED ${lineTotal.toFixed(2)}\n`;
                      
                      message += productText;
                    });
                    
                    message += "\n------------------------------\n";
                    
                    // Add savings if available
                    if (cartSummary.totalSavings && cartSummary.totalSavings > 0) {
                      message += `Subtotal: AED ${cartSummary.originalTotal?.toFixed(2)}\n`;
                      message += `Savings: -AED ${cartSummary.totalSavings.toFixed(2)}\n`;
                    }
                    
                    message += `TOTAL: AED ${cartSummary.total.toFixed(2)}\n\n`;
                    message += "Please confirm this order. Thank you!";
                    
                    const whatsappUrl = `https://wa.me/919778248100?text=${encodeURIComponent(message)}`;
                    window.open(whatsappUrl, '_blank');
                  }}
                >
                  Proceed to Checkout via WhatsApp
                </button>

                {!selectedAddressId && (
                  <div className="text-sm text-brand-main-red mb-4 text-center">
                    Please select a delivery address below to proceed with checkout
                  </div>
                )}

                <div className="text-sm text-gray-600 italic mb-4">
                  Payments will be done via WhatsApp and you will be redirected to WhatsApp.
                </div>

                <button
                  onClick={() => navigate('/store')}
                  className="w-full bg-transparent border-2 border-brand-accent-teal text-brand-accent-teal py-3 rounded-full font-semibold hover:bg-brand-accent-teal hover:text-white transition-all duration-300"
                >
                  Continue Shopping
                </button>
              </div>

              {/* Address Selection */}
              <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 p-6 mt-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Select Delivery Address</h3>
                <AddressList
                  onSelect={true}
                  onAddressSelect={setSelectedAddressId}
                  selectedAddressId={selectedAddressId}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CartPage;
