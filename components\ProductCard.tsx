
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Product } from '../types';
import { useAuth } from '../contexts/AuthContext';
import { useCart } from '../contexts/CartContext';
import { getProductUrl } from '../utils/urlUtils';
import { PlusIcon } from './common/Icon';

interface ProductCardProps {
  product: Product;
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { addToCart, isInCart, getCartItemQuantity } = useCart();
  const [isAddingToCart, setIsAddingToCart] = useState(false);

  // Check if this product is in cart
  const productInCart = isInCart(product.id);
  const cartQuantity = getCartItemQuantity(product.id);

  // Determine which colors/variants to display and how many are remaining
  // Prioritize variants over colors for display
  const hasVariants = product.variants && product.variants.length > 0;

  let displayColors: any[] = [];
  let remainingColorCount = 0;

  if (hasVariants) {
    // Display up to 2 variant colors, then show remaining count
    displayColors = product.variants!.slice(0, 2).map(variant => variant.color).filter(Boolean);
    remainingColorCount = Math.max(0, product.variants!.length - 2);
  } else if (product.colors && product.colors.length > 0) {
    // Fallback to regular colors if no variants
    displayColors = product.colors.slice(0, 2);
    if (product.availableColorCount && product.colors) {
      const displayedCount = Math.min(product.colors.length, 2);
      remainingColorCount = product.availableColorCount - displayedCount;
    } else if (product.availableColorCount) {
      remainingColorCount = product.availableColorCount;
    }
    if (remainingColorCount < 0) remainingColorCount = 0;
  }

  const handleCardClick = () => {
    navigate(getProductUrl(product.id));
  };

  const handleAddToCart = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click

    if (!user) {
      navigate('/login');
      return;
    }

    setIsAddingToCart(true);
    const result = await addToCart(product.id, 1);
    setIsAddingToCart(false);

    if (result.success) {
      // Show success feedback (you could add a toast notification here)
      console.log('Added to cart successfully');
    } else {
      // Show error feedback
      console.error('Failed to add to cart:', result.error);
      alert(result.error || 'Failed to add item to cart');
    }
  };

  // Get the image to display (variant image or default)
  const getDisplayImage = () => {
    if (hasVariants && product.selectedVariant) {
      const selectedVariant = product.variants!.find(v => v.id === product.selectedVariant);
      if (selectedVariant) {
        return selectedVariant.imageUrl;
      }
    }
    // Handle both imageUrl and image_url properties
    return product.imageUrl || product.image_url || '';
  };

  return (
    <div
      className="bg-white/90 backdrop-blur-sm rounded-2xl group flex flex-col font-sans shadow-lg border border-white/50 hover:shadow-2xl hover:bg-white/95 transition-all duration-500 cursor-pointer"
      onClick={handleCardClick}
    >
      <div className="relative bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl aspect-[3/4] overflow-hidden"> {/* Enhanced gradient background */}

        {/* Subtle pattern overlay */}
        <div className="absolute inset-0 opacity-5" style={{
          backgroundImage: `radial-gradient(circle at 2px 2px, rgba(9, 184, 166, 0.3) 1px, transparent 0)`,
          backgroundSize: '20px 20px'
        }}></div>

        <img
          src={getDisplayImage()}
          alt={product.name}
          className="relative z-10 w-full h-full object-cover"
        />
        {(displayColors.length > 0 || remainingColorCount > 0) && (
          <div className="absolute top-3 right-3 flex items-center space-x-2 z-20">
            {displayColors.map((color, index) => (
              <span
                key={index}
                className="block w-5 h-5 rounded-full border-2 border-white shadow-lg ring-1 ring-gray-200/50 transition-transform duration-300 group-hover:scale-110"
                style={{ backgroundColor: color.hex }}
                title={color.name}
                aria-label={`Color: ${color.name}`}
              ></span>
            ))}
            {(remainingColorCount > 0 || product.name === 'Gentle Hand Wash') && (
              <span
                className="text-xs font-semibold text-gray-700 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full leading-tight shadow-md border border-gray-200/50 transition-all duration-300 group-hover:bg-white group-hover:scale-105"
                aria-label={`${product.name === 'Gentle Hand Wash' ? '3' : remainingColorCount} colors available`}
              >
                {product.name === 'Gentle Hand Wash' ? '3' : remainingColorCount}
              </span>
            )}
          </div>
        )}

        {/* Floating decorative elements */}
        <div className="absolute top-2 left-2 w-3 h-3 bg-brand-accent-teal/20 rounded-full blur-sm opacity-0 group-hover:opacity-60 transition-opacity duration-500"></div>
        <div className="absolute bottom-2 right-2 w-2 h-2 bg-brand-main-red/20 rounded-full blur-sm opacity-0 group-hover:opacity-40 transition-opacity duration-500"></div>
      </div>

      {/* Enhanced Product Information */}
      <div className="pt-4 pb-5 px-4 text-left relative">
        {/* Subtle gradient background */}
        <div className="absolute inset-0 bg-gradient-to-t from-gray-50/50 to-transparent rounded-b-2xl"></div>

        <div className="relative z-10">
          <h3
              className="text-lg font-bold text-gray-900 truncate mb-1 transition-colors duration-300 group-hover:text-brand-accent-teal"
              title={product.name}
              style={{fontWeight: 700}} /* Enhanced font weight */
          >
            {product.name}
          </h3>
          {product.material && (
            <p className="text-sm text-gray-600 mt-1 truncate leading-relaxed" title={product.material}>
              {product.material}
            </p>
          )}

          {/* Enhanced Price with discount information - Fixed height container */}
          <div className="mt-3 flex items-center justify-between">
            <div className="flex flex-col min-h-[4.5rem]"> {/* Fixed minimum height for consistency */}
              {/* Discount info section - always takes same space */}
              <div className="h-6 mb-1"> {/* Fixed height container for discount info */}
                {(product.originalPrice || product.original_price) && product.discount && (
                  <div className="flex items-center space-x-2">
                    <span className="bg-brand-main-red text-white text-xs px-2 py-1 rounded-full font-bold">
                      {product.discount}
                    </span>
                    <span className="text-xs text-gray-500 line-through">
                      AED {(product.originalPrice || product.original_price)?.toFixed(2)}
                    </span>
                  </div>
                )}
              </div>

              {/* Current price */}
              <p
                  className="text-lg font-bold bg-gradient-to-r from-brand-accent-teal to-brand-main-red bg-clip-text text-transparent mb-1"
                  style={{fontWeight: 700}}
              >
                AED {product.price.toFixed(2)}
              </p>

              {/* Savings section - always takes same space */}
              <div className="h-4"> {/* Fixed height container for savings */}
                {(product.originalPrice || product.original_price) && (
                  <span className="text-xs text-brand-accent-teal font-semibold">
                    Save AED {((product.originalPrice || product.original_price || 0) - product.price).toFixed(2)}
                  </span>
                )}
              </div>
            </div>

            {/* Add to cart button with cart status */}
            <div className="relative">
              <button
                onClick={handleAddToCart}
                disabled={isAddingToCart}
                className="w-8 h-8 bg-gradient-to-r from-brand-accent-teal to-brand-main-red rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:scale-110 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed hover:shadow-lg"
                aria-label={productInCart ? `Add more to cart (currently ${cartQuantity})` : "Add to cart"}
              >
                {isAddingToCart ? (
                  <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <PlusIcon size={14} className="text-white" />
                )}
              </button>

              {/* Cart quantity badge - only visible on hover */}
              {productInCart && cartQuantity > 0 && (
                <div className="absolute -top-2 -right-2 bg-brand-main-red text-white text-xs w-5 h-5 rounded-full flex items-center justify-center font-bold shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-300">
                  {cartQuantity > 99 ? '99+' : cartQuantity}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;